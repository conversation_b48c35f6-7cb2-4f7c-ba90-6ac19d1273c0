#!/usr/bin/env python3
"""
Simple server startup script for ValidatorFinal
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🚀 Starting ValidatorFinal Server...")
print(f"📁 Working directory: {os.getcwd()}")
print(f"🐍 Python path: {src_path}")

try:
    # Import the app
    from api.app import app
    import uvicorn
    
    print("✅ Imports successful")
    print("🌐 Starting server on http://localhost:8002")
    print("📖 API Documentation: http://localhost:8002/docs")
    print("❤️  Health Check: http://localhost:8002/health")
    print("\n" + "="*50)
    
    # Start the server
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8002,
        log_level="info",
        reload=False
    )
    
except Exception as e:
    print(f"❌ Failed to start server: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
