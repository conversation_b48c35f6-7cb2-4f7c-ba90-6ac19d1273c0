# src/validation/gemini_client.py
import google.generativeai as genai
import json
from typing import Dict, Any
from .prompts import PromptManager

class GeminiValidationClient:
    def __init__(self):
        genai.configure(api_key="your-api-key")
        self.model = genai.GenerativeModel('gemini-pro')
        self.prompt_manager = PromptManager()
        
    async def validate_chunk(self, prompt: str, chunk_data: Dict, utility_rules: Dict) -> Dict:
        """Send chunk to Gemini for validation"""
        
        full_prompt = self.build_validation_prompt(prompt, chunk_data, utility_rules)
        
        try:
            response = await self.model.generate_content_async(full_prompt)
            return self.parse_gemini_response(response.text)
            
        except Exception as e:
            return {
                'error': True,
                'message': f"Gemini validation failed: {str(e)}",
                'issues': []
            }
    
    def build_validation_prompt(self, template: str, data: Dict, rules: Dict) -> str:
        """Build complete validation prompt for Gemini"""
        
        base_prompt = self.prompt_manager.get_prompt(template)
        
        prompt = f"""
        {base_prompt}
        
        UTILITY RULES TO VALIDATE AGAINST:
        {json.dumps(rules, indent=2)}
        
        DATA TO VALIDATE:
        {json.dumps(data, indent=2)}
        
        INSTRUCTIONS:
        1. Check each rule against the provided data
        2. Identify any violations or missing requirements
        3. Classify severity: CRITICAL, MAJOR, MINOR
        4. Provide specific fix recommendations
        5. Return result in JSON format as specified
        
        REQUIRED JSON RESPONSE FORMAT:
        {{
            "validation_passed": boolean,
            "issues": [
                {{
                    "severity": "CRITICAL|MAJOR|MINOR",
                    "category": "string",
                    "description": "string",
                    "fix_recommendation": "string",
                    "rejection_risk": "HIGH|MEDIUM|LOW"
                }}
            ],
            "summary": "string"
        }}
        """
        
        return prompt
    
    def parse_gemini_response(self, response_text: str) -> Dict:
        """Parse and validate Gemini response"""
        try:
            # Extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end]
            
            result = json.loads(json_str)
            
            # Validate response structure
            required_fields = ['validation_passed', 'issues', 'summary']
            if all(field in result for field in required_fields):
                return result
            else:
                return self.create_error_response("Invalid response format from Gemini")
                
        except json.JSONDecodeError:
            return self.create_error_response("Failed to parse Gemini JSON response")
    
    def create_error_response(self, error_msg: str) -> Dict:
        """Create standardized error response"""
        return {
            'validation_passed': False,
            'issues': [{
                'severity': 'CRITICAL',
                'category': 'SYSTEM_ERROR',
                'description': error_msg,
                'fix_recommendation': 'Review system configuration',
                'rejection_risk': 'HIGH'
            }],
            'summary': f'Validation failed due to system error: {error_msg}'
        }