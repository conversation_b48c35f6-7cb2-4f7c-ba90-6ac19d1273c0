#!/usr/bin/env python3
"""
Startup script for the Validator API Server
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and start the server
if __name__ == "__main__":
    try:
        print("🚀 Starting Solar Planset Validator API Server...")
        print("📁 Working directory:", os.getcwd())
        print("🐍 Python path:", sys.path[0])
        
        from api.app import app
        import uvicorn
        
        print("✅ Imports successful")
        print("🌐 Starting server on http://localhost:8002")
        print("📖 API Documentation: http://localhost:8002/docs")
        print("❤️  Health Check: http://localhost:8002/health")
        print("\n" + "="*50)

        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8002,
            log_level="info",
            reload=False
        )
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
