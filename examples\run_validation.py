# examples/run_validation.py
import asyncio
from src.validation.validator import GeminiValidator

async def main():
    # Sam<PERSON> extracted data from extractor agent
    extracted_data = {
        "system_info": {
            "capacity_ac_kw": 4.5,
            "capacity_dc_kw": 6.2,
            "has_battery": False
        },
        "documents": {
            "application": {
                "customer_name": "John Doe",
                "system_size": "4.5kW AC"
            },
            "single_line_diagram": {
                "inverter_model": "ENPHASE IQ8PLUS",
                "module_count": 14
            }
        },
        "utility": "Florida Power & Light"
    }
    
    # Initialize validator
    validator = GeminiValidator({"utility_name": "FPL"})
    
    # Run validation
    result = await validator.validate(extracted_data)
    
    print("Validation Result:")
    print(f"Status: {result.status}")
    print(f"Critical Issues: {len(result.critical_issues)}")
    print(f"Issues: {result.issues}")

if __name__ == "__main__":
    asyncio.run(main())