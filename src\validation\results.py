import json
from typing import Dict, <PERSON>, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import math
from datetime import datetime

class IssueSeverity(Enum):
    CRITICAL = "Critical"
    MAJOR = "Major" 
    MINOR = "Minor"

class SubmissionStatus(Enum):
    PASS = "Pass"
    NEEDS_REVIEW = "Needs Review"
    FAIL = "Fail"

@dataclass
class ValidationIssue:
    severity: IssueSeverity
    category: str
    description: str
    fix_recommendation: str
    rule_reference: str = ""

@dataclass
class ValidationResult:
    status: SubmissionStatus
    issues: List[ValidationIssue]
    summary: str
    timestamp: datetime = None

class SolarPlansetValidator:
    def __init__(self):
        self.issues = []
        
    def validate_planset(self, planset_json: Dict) -> ValidationResult:
        """Main validation entry point"""
        self.issues = []
        
        # Layer 1: Deterministic Rule-Based Calculations
        self._layer1_deterministic_validation(planset_json)
        
        # Layer 2: Intelligent Contextual Validation
        self._layer2_contextual_validation(planset_json)
        
        # Layer 3: Result Compilation & Status Decision
        return self._layer3_compile_results()
    
    def _layer1_deterministic_validation(self, data: Dict):
        """Layer 1: Hard utility/NEC rule validation"""
        
        # 1. Busbar Calculation (120% Rule - NEC 705.12)
        self._validate_busbar_calculation(data)
        
        # 2. System sizing checks
        self._validate_system_sizing(data)
        
        # 3. Electrical validation
        self._validate_electrical_requirements(data)
        
        # 4. Safety compliance checks
        self._validate_safety_compliance(data)
        
        # 5. Required document completeness
        self._validate_document_completeness(data)
        
        # 6. Critical gaps check
        self._validate_critical_gaps(data)
    
    def _validate_busbar_calculation(self, data: Dict):
        """Validate NEC 705.12 busbar calculation"""
        try:
            electrical = data.get('electrical_validation', {})
            main_panel = electrical.get('main_panel', {})
            
            bus_rating = main_panel.get('bus_rating', 0)
            main_breaker = main_panel.get('main_breaker_derated', 0)
            solar_breaker = main_panel.get('solar_breaker', 0)
            
            if not all([bus_rating, main_breaker, solar_breaker]):
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.CRITICAL,
                    category="Electrical",
                    description="Missing critical busbar calculation parameters",
                    fix_recommendation="Provide bus rating, main breaker, and solar breaker amperage",
                    rule_reference="NEC 705.12"
                ))
                return
            
            # Calculate 120% rule
            max_allowed = bus_rating * 1.2
            total_load = main_breaker + solar_breaker
            
            bus_calc = main_panel.get('bus_calculation', {})
            if not bus_calc.get('pass', False) or total_load > max_allowed:
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.CRITICAL,
                    category="Electrical",
                    description=f"Busbar calculation fails: {total_load}A > {max_allowed}A (120% of {bus_rating}A)",
                    fix_recommendation="Reduce solar breaker size, upgrade panel bus rating, or relocate breakers",
                    rule_reference="NEC 705.12(B)(2)(3)"
                ))
                
        except Exception as e:
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Data",
                description=f"Error validating busbar calculation: {str(e)}",
                fix_recommendation="Verify electrical panel data is complete and correct"
            ))
    
    def _validate_system_sizing(self, data: Dict):
        """Validate system sizing requirements"""
        try:
            system_profile = data.get('system_profile', {})
            capacity_ac = system_profile.get('capacity_ac_kw', 0)
            capacity_dc = system_profile.get('capacity_dc_kw', 0)
            
            # Check AC/DC ratio (typical inverter efficiency)
            if capacity_ac and capacity_dc:
                ratio = capacity_ac / capacity_dc
                if ratio > 1.4:  # Unusually high AC/DC ratio
                    self.issues.append(ValidationIssue(
                        severity=IssueSeverity.MAJOR,
                        category="System Design",
                        description=f"AC/DC ratio ({ratio:.2f}) is unusually high",
                        fix_recommendation="Verify inverter and module specifications"
                    ))
                    
            # Module calculation verification
            modules = system_profile.get('modules', {})
            if modules:
                calc_capacity = (modules.get('quantity', 0) * modules.get('power_w', 0)) / 1000
                if abs(calc_capacity - capacity_dc) > 0.1:
                    self.issues.append(ValidationIssue(
                        severity=IssueSeverity.MAJOR,
                        category="System Design",
                        description=f"Module calculation mismatch: {calc_capacity}kW vs {capacity_dc}kW",
                        fix_recommendation="Verify module count and wattage specifications"
                    ))
                    
        except Exception as e:
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MINOR,
                category="Data",
                description=f"Error validating system sizing: {str(e)}",
                fix_recommendation="Review system capacity specifications"
            ))
    
    def _validate_electrical_requirements(self, data: Dict):
        """Validate electrical installation requirements"""
        electrical = data.get('electrical_validation', {})
        conductors = electrical.get('conductors', {})
        
        # Check for missing EGC specification
        if conductors.get('egc_size') == "NOT SPECIFIED":
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.CRITICAL,
                category="Electrical",
                description="Equipment Grounding Conductor (EGC) size not specified",
                fix_recommendation="Specify EGC wire size per NEC 250.122",
                rule_reference="NEC 250.122"
            ))
        
        # Grounding validation
        grounding = electrical.get('grounding', {})
        if not grounding.get('method'):
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Electrical",
                description="Grounding method not specified",
                fix_recommendation="Specify grounding method (exothermic welding or compression)",
                rule_reference="NEC 250.8"
            ))
    
    def _validate_safety_compliance(self, data: Dict):
        """Validate safety and fire code compliance"""
        safety = data.get('safety_compliance', {})
        
        # Rapid shutdown validation
        rapid_shutdown = safety.get('rapid_shutdown', {})
        if not rapid_shutdown.get('compliance', False):
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.CRITICAL,
                category="Safety",
                description="Rapid shutdown compliance not verified",
                fix_recommendation="Ensure rapid shutdown devices meet NEC 690.12 requirements",
                rule_reference="NEC 690.12"
            ))
        
        # Fire setbacks
        fire_setbacks = safety.get('fire_setbacks', {})
        edges = fire_setbacks.get('edges_in', 0)
        ridge = fire_setbacks.get('ridge_in', 0)
        
        if edges < 36:  # Typical requirement
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Safety",
                description=f"Edge setback ({edges}\") may not meet local fire code",
                fix_recommendation="Verify local fire code requirements (typically 36\" minimum)",
                rule_reference="Local Fire Code"
            ))
            
        if ridge < 36:
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Safety",
                description=f"Ridge setback ({ridge}\") may not meet local fire code",
                fix_recommendation="Verify local fire code requirements (typically 36\" minimum)",
                rule_reference="Local Fire Code"
            ))
    
    def _validate_document_completeness(self, data: Dict):
        """Validate required document completeness"""
        completeness = data.get('field_level_document_completeness', {})
        
        for doc_name, doc_data in completeness.items():
            if isinstance(doc_data, dict):
                missing_items = [key for key, value in doc_data.items() if value is False]
                if missing_items:
                    self.issues.append(ValidationIssue(
                        severity=IssueSeverity.MAJOR,
                        category="Documentation",
                        description=f"{doc_name} missing required information: {', '.join(missing_items)}",
                        fix_recommendation=f"Complete missing information in {doc_name}"
                    ))
    
    def _validate_critical_gaps(self, data: Dict):
        """Check for critical gaps identified in the planset"""
        critical_gaps = data.get('critical_gaps', [])
        
        for gap in critical_gaps:
            severity = IssueSeverity.CRITICAL
            if "discrepancies" in gap.lower() or "conflict" in gap.lower():
                severity = IssueSeverity.MAJOR
            elif "missing" in gap.lower() and ("stamp" in gap.lower() or "egc" in gap.lower()):
                severity = IssueSeverity.CRITICAL
            
            self.issues.append(ValidationIssue(
                severity=severity,
                category="Critical Gap",
                description=gap,
                fix_recommendation=self._get_gap_recommendation(gap)
            ))
    
    def _get_gap_recommendation(self, gap: str) -> str:
        """Generate fix recommendations for critical gaps"""
        gap_lower = gap.lower()
        
        if "egc" in gap_lower:
            return "Specify EGC conductor size per NEC 250.122 throughout all electrical documents"
        elif "structural engineering stamp" in gap_lower:
            return "Obtain professional structural engineer's stamp for roof loading calculations"
        elif "utility conflict" in gap_lower:
            return "Resolve utility provider discrepancy - verify correct utility and fault current values"
        elif "conduit height" in gap_lower:
            return "Standardize conduit height specifications across all documents"
        elif "battery clearance" in gap_lower:
            return "Verify and standardize battery clearance requirements per manufacturer specifications"
        else:
            return "Address the identified gap to ensure planset completeness"
    
    def _layer2_contextual_validation(self, data: Dict):
        """Layer 2: Intelligent contextual validation"""
        # This would typically call an LLM for contextual analysis
        # For now, we'll implement some contextual checks directly

        # Check project information completeness
        self._validate_project_information(data)

        # Validate system profile consistency
        self._validate_system_profile(data)

        # Check cross-document consistency
        self._validate_cross_document_consistency(data)

        # Validate utility requirements
        self._validate_utility_requirements(data)

        # Check structural requirements
        self._validate_structural_requirements(data)
    
    def _validate_cross_document_consistency(self, data: Dict):
        """Validate consistency across documents"""
        consistency = data.get('cross_document_consistency', {})
        
        for item_type, item_data in consistency.items():
            if isinstance(item_data, dict) and not item_data.get('consistent', True):
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.MAJOR,
                    category="Consistency",
                    description=f"Inconsistent {item_type} specifications across documents",
                    fix_recommendation=f"Standardize {item_type} specifications across all planset documents"
                ))
    
    def _validate_utility_requirements(self, data: Dict):
        """Validate utility-specific requirements"""
        utility_req = data.get('utility_requirements', {})
        project_info = data.get('project_info', {})
        
        # Check utility provider consistency
        if project_info.get('utility') != utility_req.get('provider'):
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Utility",
                description="Utility provider mismatch between project info and requirements",
                fix_recommendation="Verify and correct utility provider information"
            ))
        
        # Check required utility equipment
        requirements = utility_req.get('requirements', {})
        if requirements.get('external_disconnect') and not data.get('safety_compliance', {}).get('disconnects'):
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.CRITICAL,
                category="Utility",
                description="Utility requires external disconnect but none specified",
                fix_recommendation="Add required external disconnect per utility requirements"
            ))
    
    def _validate_structural_requirements(self, data: Dict):
        """Validate structural requirements"""
        structural = data.get('structural_data', {})
        
        # Check for structural engineering requirements
        if not structural:
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MAJOR,
                category="Structural",
                description="Structural data missing",
                fix_recommendation="Provide structural analysis and mounting specifications"
            ))
            return
        
        # Validate wind/snow loads
        wind_load = structural.get('wind_load_mph', 0)
        if wind_load < 90:  # Typical minimum requirement
            self.issues.append(ValidationIssue(
                severity=IssueSeverity.MINOR,
                category="Structural",
                description=f"Wind load rating ({wind_load} mph) may be below typical requirements",
                fix_recommendation="Verify wind load requirements for local jurisdiction"
            ))
    
    def _layer3_compile_results(self) -> ValidationResult:
        """Layer 3: Compile results and determine submission status"""
        
        # Count issues by severity
        critical_count = len([i for i in self.issues if i.severity == IssueSeverity.CRITICAL])
        major_count = len([i for i in self.issues if i.severity == IssueSeverity.MAJOR])
        minor_count = len([i for i in self.issues if i.severity == IssueSeverity.MINOR])
        
        # Determine submission status
        if critical_count > 0:
            status = SubmissionStatus.FAIL
            summary = f"FAIL: {critical_count} critical issue(s) must be resolved before submission."
        elif major_count > 0:
            status = SubmissionStatus.NEEDS_REVIEW
            summary = f"NEEDS REVIEW: {major_count} major issue(s) should be addressed before submission."
        elif minor_count > 0:
            status = SubmissionStatus.NEEDS_REVIEW
            summary = f"NEEDS REVIEW: {minor_count} minor issue(s) identified but submission may proceed."
        else:
            status = SubmissionStatus.PASS
            summary = "PASS: Planset meets technical and compliance requirements for submission."
        
        return ValidationResult(
            status=status,
            issues=self.issues,
            summary=summary,
            timestamp=datetime.now()
        )
    
    def generate_report(self, result: ValidationResult) -> str:
        """Generate a detailed validation report"""
        report = f"""
=== SOLAR PLANSET VALIDATION REPORT ===

FINAL SUBMISSION STATUS: {result.status.value}

SUMMARY: {result.summary}

ISSUES FOUND: {len(result.issues)}

"""
        
        # Group issues by severity
        for severity in [IssueSeverity.CRITICAL, IssueSeverity.MAJOR, IssueSeverity.MINOR]:
            severity_issues = [i for i in result.issues if i.severity == severity]
            if severity_issues:
                report += f"\n{severity.value.upper()} ISSUES ({len(severity_issues)}):\n"
                report += "=" * (len(severity.value) + 15) + "\n"
                
                for i, issue in enumerate(severity_issues, 1):
                    report += f"\n{i}. [{issue.category}] {issue.description}\n"
                    report += f"   Fix: {issue.fix_recommendation}\n"
                    if issue.rule_reference:
                        report += f"   Reference: {issue.rule_reference}\n"
        
        if not result.issues:
            report += "\nNo issues found. Planset is ready for submission.\n"
        
        return report

    # Additional Validation Methods for Enhanced Coverage

    def _validate_project_information(self, data: Dict):
        """Validate project information completeness and accuracy"""
        project_info = data.get('project_info', {})

        # Check required project fields
        required_fields = ['customer', 'address', 'utility', 'contractor']
        for field in required_fields:
            if not project_info.get(field) or project_info.get(field) == 'NOT_SPECIFIED':
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.MAJOR,
                    category="Project Information",
                    description=f"Missing or incomplete {field} information",
                    fix_recommendation=f"Provide complete {field} information in project details"
                ))

        # Validate contractor information
        contractor = project_info.get('contractor', {})
        if isinstance(contractor, dict):
            contractor_fields = ['name', 'license', 'contact']
            for field in contractor_fields:
                if not contractor.get(field) or contractor.get(field) == 'NOT_SPECIFIED':
                    self.issues.append(ValidationIssue(
                        severity=IssueSeverity.MINOR,
                        category="Contractor Information",
                        description=f"Missing contractor {field}",
                        fix_recommendation=f"Provide contractor {field} information"
                    ))

    def _validate_system_profile(self, data: Dict):
        """Validate system profile consistency and completeness"""
        system_profile = data.get('system_profile', {})

        # Check system capacity consistency
        capacity_dc = system_profile.get('capacity_dc_kw', 0)
        capacity_ac = system_profile.get('capacity_ac_kw', 0)

        if capacity_dc and capacity_ac:
            # Check reasonable AC/DC ratio
            ratio = capacity_ac / capacity_dc if capacity_dc > 0 else 0
            if ratio > 1.0:  # AC cannot be greater than DC
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.CRITICAL,
                    category="System Profile",
                    description=f"Invalid AC/DC ratio: AC ({capacity_ac}kW) > DC ({capacity_dc}kW)",
                    fix_recommendation="Verify system capacity calculations - AC cannot exceed DC capacity"
                ))
            elif ratio < 0.7:  # Very low efficiency
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.MINOR,
                    category="System Profile",
                    description=f"Low AC/DC ratio ({ratio:.2f}) may indicate inefficient design",
                    fix_recommendation="Review inverter selection and system design efficiency"
                ))

        # Validate module specifications
        modules = system_profile.get('modules', {})
        if modules:
            quantity = modules.get('quantity', 0)
            power_w = modules.get('power_w', 0)

            if quantity and power_w:
                calculated_dc = (quantity * power_w) / 1000
                if abs(calculated_dc - capacity_dc) > 0.5:  # Allow 0.5kW tolerance
                    self.issues.append(ValidationIssue(
                        severity=IssueSeverity.MAJOR,
                        category="System Profile",
                        description=f"Module calculation mismatch: {calculated_dc}kW calculated vs {capacity_dc}kW specified",
                        fix_recommendation="Verify module quantity and wattage specifications"
                    ))

        # Check battery integration if present
        battery = system_profile.get('battery', {})
        if battery.get('model', 'NOT_SPECIFIED') != 'NOT_SPECIFIED':
            if not battery.get('capacity_kwh') or battery.get('capacity_kwh') == 'NOT_SPECIFIED':
                self.issues.append(ValidationIssue(
                    severity=IssueSeverity.MAJOR,
                    category="Battery System",
                    description="Battery model specified but capacity not provided",
                    fix_recommendation="Provide battery capacity specifications"
                ))

# Example usage function
def validate_planset_from_json(json_data: str) -> str:
    """Main validation function"""
    try:
        # Parse JSON data
        planset_data = json.loads(json_data) if isinstance(json_data, str) else json_data
        
        # Create validator and run validation
        validator = SolarPlansetValidator()
        result = validator.validate_planset(planset_data)
        
        # Generate and return report
        return validator.generate_report(result)
        
    except json.JSONDecodeError as e:
        return f"Error: Invalid JSON format - {str(e)}"
    except Exception as e:
        return f"Error: Validation failed - {str(e)}"

