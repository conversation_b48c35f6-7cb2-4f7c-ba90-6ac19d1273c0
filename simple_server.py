#!/usr/bin/env python3
"""
Simple server startup script that bypasses import issues
"""

import uvicorn
import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🚀 Starting ValidatorFinal Server...")
print(f"📁 Working directory: {os.getcwd()}")
print(f"🐍 Python path: {src_path}")
print("🌐 Starting server on http://localhost:8002")
print("📖 API Documentation: http://localhost:8002/docs")
print("❤️  Health Check: http://localhost:8002/health")
print("\n" + "="*50)

# Start the server directly with uvicorn
if __name__ == "__main__":
    uvicorn.run(
        "api.app:app",
        host="0.0.0.0", 
        port=8002,
        log_level="info",
        reload=False
    )
