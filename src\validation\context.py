# src/validation/context.py
"""
Validation Context for Solar Permit Data
Updated to work with the new solar permit JSON format from extractor
"""

from typing import Dict, Any, Optional, List

class ValidationContext:
    """
    Validation context that provides structured access to solar permit data
    Compatible with the new solar permit JSON format
    """

    def __init__(self, extracted_data: Dict, utility):
        self.data = extracted_data
        self.utility = utility

    # System Profile Methods
    def get_system_capacity_ac(self) -> Optional[float]:
        """Extract AC system capacity from system_profile"""
        return self.data.get('system_profile', {}).get('capacity_ac_kw')

    def get_system_capacity_dc(self) -> Optional[float]:
        """Extract DC system capacity from system_profile"""
        return self.data.get('system_profile', {}).get('capacity_dc_kw')

    def get_system_type(self) -> str:
        """Get system type (SOLAR_PLUS_STORAGE or SOLAR_ONLY)"""
        return self.data.get('system_profile', {}).get('type', 'UNKNOWN')

    def has_battery(self) -> bool:
        """Check if system has battery storage"""
        battery_model = self.data.get('system_profile', {}).get('battery', {}).get('model', 'NOT_SPECIFIED')
        return battery_model != 'NOT_SPECIFIED' and battery_model != ''

    def get_battery_specs(self) -> Dict:
        """Get battery specifications"""
        return self.data.get('system_profile', {}).get('battery', {})

    def get_module_specs(self) -> Dict:
        """Get solar module specifications"""
        return self.data.get('system_profile', {}).get('modules', {})

    # Project Information Methods
    def get_project_info(self) -> Dict:
        """Get project information"""
        return self.data.get('project_info', {})

    def get_customer_name(self) -> str:
        """Get customer name"""
        return self.data.get('project_info', {}).get('customer', 'NOT_SPECIFIED')

    def get_property_address(self) -> str:
        """Get property address"""
        return self.data.get('project_info', {}).get('address', 'NOT_SPECIFIED')

    def get_utility_provider(self) -> str:
        """Get utility provider"""
        return self.data.get('project_info', {}).get('utility', 'NOT_SPECIFIED')

    def get_contractor_info(self) -> Dict:
        """Get contractor information"""
        return self.data.get('project_info', {}).get('contractor', {})

    # Electrical Validation Methods
    def get_electrical_validation(self) -> Dict:
        """Get electrical validation data"""
        return self.data.get('electrical_validation', {})

    def get_main_panel_info(self) -> Dict:
        """Get main panel information"""
        return self.data.get('electrical_validation', {}).get('main_panel', {})

    def get_conductor_specs(self) -> Dict:
        """Get conductor specifications"""
        return self.data.get('electrical_validation', {}).get('conductors', {})

    def get_grounding_info(self) -> Dict:
        """Get grounding information"""
        return self.data.get('electrical_validation', {}).get('grounding', {})

    # Safety Compliance Methods
    def get_safety_compliance(self) -> Dict:
        """Get safety compliance data"""
        return self.data.get('safety_compliance', {})

    def get_rapid_shutdown_info(self) -> Dict:
        """Get rapid shutdown information"""
        return self.data.get('safety_compliance', {}).get('rapid_shutdown', {})

    def get_fire_setbacks(self) -> Dict:
        """Get fire setback information"""
        return self.data.get('safety_compliance', {}).get('fire_setbacks', {})

    def get_disconnect_info(self) -> Dict:
        """Get disconnect information"""
        return self.data.get('safety_compliance', {}).get('disconnects', {})

    # Structural Data Methods
    def get_structural_data(self) -> Dict:
        """Get structural data"""
        return self.data.get('structural_data', {})

    def get_roof_mounting_info(self) -> Dict:
        """Get roof mounting information"""
        return self.data.get('structural_data', {}).get('roof_mounting', {})

    # Utility Requirements Methods
    def get_utility_requirements(self) -> Dict:
        """Get utility requirements"""
        return self.data.get('utility_requirements', {})

    # Document Completeness Methods
    def get_document_completeness(self) -> Dict:
        """Get document completeness information"""
        return self.data.get('field_level_document_completeness', {})

    def get_cross_document_consistency(self) -> Dict:
        """Get cross-document consistency information"""
        return self.data.get('cross_document_consistency', {})

    def get_critical_gaps(self) -> List[str]:
        """Get critical gaps identified"""
        return self.data.get('critical_gaps', [])

    # Legacy compatibility methods for existing chunks
    def get_application_data(self) -> Dict:
        """Get application form data (legacy compatibility)"""
        return {
            'customer': self.get_customer_name(),
            'address': self.get_property_address(),
            'utility': self.get_utility_provider(),
            'contractor': self.get_contractor_info(),
            'system_capacity_dc': self.get_system_capacity_dc(),
            'system_capacity_ac': self.get_system_capacity_ac(),
            'system_type': self.get_system_type()
        }

    def get_site_plan_data(self) -> Dict:
        """Get site plan data (legacy compatibility)"""
        return {
            'address': self.get_property_address(),
            'fire_setbacks': self.get_fire_setbacks(),
            'structural_data': self.get_structural_data()
        }

    def get_single_line_data(self) -> Dict:
        """Get single line diagram data (legacy compatibility)"""
        return {
            'electrical_validation': self.get_electrical_validation(),
            'safety_compliance': self.get_safety_compliance(),
            'equipment': {
                'modules': self.get_module_specs(),
                'battery': self.get_battery_specs()
            }
        }

    def get_spec_sheet_data(self) -> Dict:
        """Get specification sheet data (legacy compatibility)"""
        return {
            'modules': self.get_module_specs(),
            'battery': self.get_battery_specs(),
            'system_profile': self.data.get('system_profile', {})
        }

    def get_inverter_specs(self) -> Dict:
        """Get inverter specifications (legacy compatibility)"""
        # In the new format, inverter info might be in system_profile or electrical_validation
        return {
            'capacity_ac_kw': self.get_system_capacity_ac(),
            'type': self.get_system_type()
        }

    def get_safety_equipment(self) -> Dict:
        """Get safety equipment information"""
        return {
            'rapid_shutdown': self.get_rapid_shutdown_info(),
            'disconnects': self.get_disconnect_info(),
            'grounding': self.get_grounding_info()
        }

    def get_interconnection_data(self) -> Dict:
        """Get interconnection data"""
        return {
            'utility_requirements': self.get_utility_requirements(),
            'electrical_validation': self.get_electrical_validation(),
            'main_panel': self.get_main_panel_info()
        }

    def get_cross_document_equipment_refs(self) -> Dict:
        """Get equipment references across documents for consistency checking"""
        return self.get_cross_document_consistency()

    def get_cross_document_capacity_refs(self) -> Dict:
        """Get capacity references across documents"""
        return {
            'system_profile_dc': self.get_system_capacity_dc(),
            'system_profile_ac': self.get_system_capacity_ac(),
            'modules_calculated': self._calculate_module_capacity()
        }

    def get_cross_document_customer_refs(self) -> Dict:
        """Get customer references across documents"""
        return {
            'project_info': self.get_project_info(),
            'contractor': self.get_contractor_info()
        }

    def get_all_data(self) -> Dict:
        """Get all extracted data"""
        return self.data

    # Helper Methods
    def _calculate_module_capacity(self) -> float:
        """Calculate total module capacity"""
        modules = self.get_module_specs()
        quantity = modules.get('quantity', 0)
        power_w = modules.get('power_w', 0)
        return (quantity * power_w) / 1000.0  # Convert to kW

    def is_data_complete(self) -> bool:
        """Check if essential data is present"""
        essential_fields = [
            self.get_customer_name(),
            self.get_property_address(),
            self.get_system_capacity_dc(),
            self.get_utility_provider()
        ]
        return all(field != 'NOT_SPECIFIED' and field for field in essential_fields)

    def get_validation_summary(self) -> Dict:
        """Get summary information for validation"""
        return {
            'customer': self.get_customer_name(),
            'address': self.get_property_address(),
            'utility': self.get_utility_provider(),
            'system_type': self.get_system_type(),
            'capacity_dc_kw': self.get_system_capacity_dc(),
            'capacity_ac_kw': self.get_system_capacity_ac(),
            'has_battery': self.has_battery(),
            'critical_gaps_count': len(self.get_critical_gaps()),
            'data_complete': self.is_data_complete()
        }