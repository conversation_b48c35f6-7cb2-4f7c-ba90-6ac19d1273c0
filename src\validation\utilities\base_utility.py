"""
Base utility class for utility-specific validation rules
"""

from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

class BaseUtility(ABC):
    """Base class for utility-specific validation rules"""

    def __init__(self, utility_name: str):
        self.utility_name = utility_name
        self.rules = {}

    @abstractmethod
    def get_interconnection_requirements(self) -> Dict[str, Any]:
        """Get utility-specific interconnection requirements"""
        pass

    @abstractmethod
    def validate_system_size_limits(self, system_capacity_kw: float) -> List[str]:
        """Validate system size against utility limits"""
        pass

    @abstractmethod
    def get_required_documents(self) -> List[str]:
        """Get list of required documents for this utility"""
        pass

    def get_utility_name(self) -> str:
        """Get the utility name"""
        return self.utility_name

    def validate_net_metering_eligibility(self, system_data: Dict) -> List[str]:
        """Base validation for net metering eligibility"""
        issues = []

        # Basic checks that apply to most utilities
        capacity = system_data.get('capacity_dc_kw', 0)
        if capacity <= 0:
            issues.append("System capacity must be greater than 0")

        return issues

    def get_setback_requirements(self) -> Dict[str, float]:
        """Get fire setback requirements (default NEC values)"""
        return {
            "edges_in": 36,  # 3 feet from edges
            "ridge_in": 36,  # 3 feet from ridge
            "hip_in": 36     # 3 feet from hip
        }


class DefaultUtility(BaseUtility):
    """Default utility implementation for generic validation"""

    def __init__(self):
        super().__init__("base")

    def get_interconnection_requirements(self) -> Dict[str, Any]:
        """Get default interconnection requirements"""
        return {
            "max_system_size_kw": 100,
            "net_metering_available": True,
            "external_disconnect_required": False,
            "production_meter_required": False,
            "study_required_threshold_kw": 50
        }

    def validate_system_size_limits(self, system_capacity_kw: float) -> List[str]:
        """Validate system size against default limits"""
        issues = []

        if system_capacity_kw > 100:
            issues.append(f"System capacity ({system_capacity_kw}kW) exceeds maximum allowed (100kW)")

        if system_capacity_kw <= 0:
            issues.append("System capacity must be greater than 0")

        return issues

    def get_required_documents(self) -> List[str]:
        """Get list of required documents for default utility"""
        return [
            "Application form",
            "Single line diagram",
            "Site plan",
            "Equipment specification sheets",
            "Structural engineering report (if required)",
            "Electrical permit"
        ]

    def get_tier_rules(self) -> Dict[str, Any]:
        """Get utility tier rules for system sizing"""
        return {
            "tier1_max_kw": 10,
            "tier2_max_kw": 50,
            "tier3_max_kw": 100,
            "study_required_above_kw": 100
        }