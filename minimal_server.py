#!/usr/bin/env python3
"""
Minimal server startup script that avoids import issues
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🚀 Starting ValidatorFinal Server...")
print(f"📁 Working directory: {os.getcwd()}")
print(f"🐍 Python path: {src_path}")

# Change to src directory and run uvicorn directly
os.chdir(src_path)

print("🌐 Starting server on http://localhost:8002")
print("📖 API Documentation: http://localhost:8002/docs")
print("❤️  Health Check: http://localhost:8002/health")
print("\n" + "="*50)

# Use os.system to run uvicorn directly
os.system("uvicorn api.app:app --host 0.0.0.0 --port 8002 --log-level info")
