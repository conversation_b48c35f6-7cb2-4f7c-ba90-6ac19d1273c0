#!/usr/bin/env python3
"""
Debug script to isolate import issues
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

print("🔍 Testing imports step by step...")

try:
    print("1. Importing FastAPI...")
    from fastapi import FastAPI
    print("   ✅ FastAPI imported successfully")
    
    print("2. Importing validation.validator...")
    from validation.validator import ThreeLayerValidator, ValidationMode, validate_solar_planset
    print("   ✅ validation.validator imported successfully")
    
    print("3. Importing validation.results...")
    from validation.results import validate_planset_from_json
    print("   ✅ validation.results imported successfully")
    
    print("4. Importing api.app...")
    from api.app import app
    print("   ✅ api.app imported successfully")
    
    print("\n🎉 All imports successful! No test code should have run.")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
