# Three-Layer Solar Planset Validation System

A comprehensive validation system for solar permit plansets that combines deterministic rule-based validation with AI-powered contextual analysis.

## 🏗️ Architecture Overview

The validation system is organized into three distinct layers:

### Layer 1: Deterministic Rule-Based Calculations
- **Purpose**: Fast, reliable validation of hard requirements
- **Coverage**: NEC codes, utility rules, safety requirements, busbar calculations
- **Confidence**: 100% (deterministic rules)
- **Processing**: Synchronous, immediate results

### Layer 2: Intelligent Contextual Validation  
- **Purpose**: AI-powered analysis for complex relationships and edge cases
- **Coverage**: Cross-document consistency, contextual understanding, utility-specific nuances
- **Confidence**: Variable (based on AI model confidence)
- **Processing**: Asynchronous, parallel chunk validation

### Layer 3: Result Compilation & Status Decision
- **Purpose**: Intelligent aggregation, deduplication, and final status determination
- **Coverage**: Issue prioritization, submission readiness assessment
- **Confidence**: Weighted combination of Layer 1 & 2
- **Processing**: Synchronous compilation and decision logic

## 📁 Project Structure

```
validatorFinal/
├── src/
│   ├── api/
│   │   ├── app.py              # FastAPI application
│   │   └── models.py           # Pydantic models
│   ├── validation/
│   │   ├── validator.py        # Main three-layer validator
│   │   ├── results.py          # Deterministic validation logic
│   │   ├── context.py          # Data context management
│   │   ├── chunks.py           # AI validation chunking
│   │   ├── gemini_client.py    # AI client interface
│   │   ├── streams/            # Validation stream modules
│   │   └── utilities/          # Utility-specific plugins
│   └── utils.py                # Utility functions
├── config/                     # Configuration files
├── data/                       # Reference data
├── tests/                      # Test files
├── requirements.txt            # Dependencies
└── test_three_layer_validation.py  # Integration test
```

## 🚀 Quick Start

### 1. Installation

```bash
cd validatorFinal
pip install -r requirements.txt
```

### 2. Run Tests

```bash
python test_three_layer_validation.py
```

### 3. Start API Server

```bash
cd src
python -m api.app
```

The API will be available at `http://localhost:8000`

### 4. API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

## 📊 Usage Examples

### Quick Validation (Layer 1 Only)

```python
from validation.results import validate_planset_from_json

# Quick deterministic validation
result = validate_planset_from_json(planset_data)
print(result)
```

### Full Three-Layer Validation

```python
import asyncio
from validation.validator import validate_solar_planset, ValidationMode

async def validate():
    utility_config = {"utility_name": "aps"}
    
    result = await validate_solar_planset(
        planset_data, 
        utility_config, 
        ValidationMode.FULL_VALIDATION
    )
    
    print(f"Status: {result['final_status']}")
    print(f"Issues: {result['issues_summary']}")

asyncio.run(validate())
```

### API Usage

```bash
# Full validation
curl -X POST "http://localhost:8000/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "planset_data": {...},
    "utility_name": "aps",
    "validation_mode": "full"
  }'

# Quick validation
curl -X POST "http://localhost:8000/validate/quick" \
  -H "Content-Type: application/json" \
  -d '{"planset_data": {...}}'
```

## 🔧 Configuration

### Validation Modes

- **FULL_VALIDATION**: All three layers (recommended)
- **QUICK_CHECK**: Layer 1 + simplified Layer 2
- **CRITICAL_ONLY**: Layer 1 critical issues only

### Utility Support

- **APS**: Arizona Public Service
- **FPL**: Florida Power & Light  
- **SRP**: Salt River Project
- **BASE**: Generic utility rules

## 📋 Input Data Format

The validator expects solar permit data in the new JSON format from the extractor:

```json
{
  "project_info": {
    "customer": "John Smith",
    "address": "123 Solar St",
    "utility": "APS",
    "contractor": {...}
  },
  "system_profile": {
    "type": "SOLAR_PLUS_STORAGE",
    "capacity_dc_kw": 12.8,
    "capacity_ac_kw": 10.0,
    "modules": {...},
    "battery": {...}
  },
  "electrical_validation": {...},
  "safety_compliance": {...},
  "structural_data": {...},
  "utility_requirements": {...},
  "critical_gaps": [...]
}
```

## 📈 Validation Results

### Status Levels

- **PASS**: Ready for submission
- **NEEDS_REVIEW**: Minor issues, submission possible
- **NEEDS_MAJOR_REVISION**: Significant issues require fixes
- **FAIL**: Critical issues prevent submission

### Issue Severity

- **CRITICAL**: Must fix before submission
- **MAJOR**: Should fix for better approval chances  
- **MINOR**: Optional improvements

## 🔍 Key Features

### ✅ Comprehensive Coverage
- NEC code compliance
- Utility-specific requirements
- Safety and structural validation
- Cross-document consistency
- Critical gap identification

### ✅ Intelligent Processing
- AI-powered contextual analysis
- Parallel chunk processing
- Issue deduplication and prioritization
- Confidence scoring

### ✅ Integration Ready
- RESTful API interface
- Compatible with extractor output
- Caching and history tracking
- Detailed reporting

## 🛠️ Development

### Adding New Validation Rules

1. **Layer 1 (Deterministic)**: Add methods to `results.py`
2. **Layer 2 (AI)**: Update chunk templates in `chunks.py`
3. **Utility-Specific**: Create plugins in `utilities/`

### Testing

```bash
# Run integration tests
python test_three_layer_validation.py

# Run specific validation tests
python -m pytest tests/
```

## 📞 Integration with Extractor

The validator is designed to work seamlessly with the extractor's output:

1. **Extractor** → Produces solar permit JSON
2. **Validator** → Validates against utility requirements  
3. **Frontend** → Displays validation results

This creates a complete pipeline from document analysis to submission readiness assessment.
