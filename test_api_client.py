#!/usr/bin/env python3
"""
Test client for the Validator API
"""

import requests
import json
import time

def test_api_endpoints():
    """Test all API endpoints"""
    base_url = "http://localhost:8002"
    
    print("🌞 Testing Solar Validator API")
    print("=" * 50)
    
    # Test 1: Health check
    print("🏥 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: Root endpoint
    print("\n🏠 Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ Root endpoint accessible")
            print(f"   Content type: {response.headers.get('content-type')}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    
    # Test 3: Quick validation
    print("\n⚡ Testing quick validation endpoint...")
    test_data = {
        "planset_data": {
            "project_info": {
                "customer": "Test Customer",
                "address": "123 Test Street, Phoenix, AZ",
                "utility": "APS",
                "contractor": "Test Solar Company"
            },
            "system_profile": {
                "capacity_dc_kw": 10.5,
                "capacity_ac_kw": 10.0,
                "module_count": 30,
                "inverter_count": 1
            },
            "electrical_validation": {
                "main_panel_rating": 200,
                "available_space": 4
            }
        },
        "utility_config": {
            "utility_name": "aps"
        },
        "validation_mode": "quick"
    }
    
    try:
        response = requests.post(
            f"{base_url}/validate/quick",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Quick validation successful")
            result = response.json()
            print(f"   Status: {result.get('status', 'Unknown')}")
            print(f"   Issues: {len(result.get('issues', []))}")
            print(f"   Processing time: {result.get('processing_time_seconds', 0):.2f}s")
        else:
            print(f"❌ Quick validation failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Quick validation error: {e}")
    
    # Test 4: Full validation
    print("\n🔍 Testing full validation endpoint...")
    full_test_data = {
        "planset_data": test_data["planset_data"],
        "utility_config": test_data["utility_config"],
        "validation_mode": "full"
    }
    
    try:
        response = requests.post(
            f"{base_url}/validate",
            json=full_test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Full validation successful")
            result = response.json()
            print(f"   Validation ID: {result.get('validation_id', 'Unknown')}")
            print(f"   Status: {result.get('final_status', 'Unknown')}")
            print(f"   Confidence: {result.get('confidence_score', 0):.2f}")
        else:
            print(f"❌ Full validation failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Full validation error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API testing completed!")

if __name__ == "__main__":
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    test_api_endpoints()
