# src/validation/chunks.py
"""
Enhanced Validation Chunker for Solar Permit Data
Creates intelligent chunks for AI-powered contextual validation
"""

from typing import Dict, List
from .utilities.base_utility import BaseUtility

class ValidationChunker:
    """
    Creates validation chunks optimized for AI analysis
    Each chunk focuses on specific validation aspects with relevant data and rules
    """

    def __init__(self, utility: BaseUtility):
        self.utility = utility

    def create_chunks(self, context) -> List[Dict]:
        """Create validation chunks based on utility requirements and data completeness"""
        chunks = []

        # Only create chunks for areas where we have sufficient data
        if context.get_system_capacity_dc() and context.get_system_capacity_ac():
            chunks.append(self.create_tier_chunk(context))

        if context.get_project_info() or context.get_electrical_validation():
            chunks.append(self.create_document_chunk(context))

        if context.get_module_specs() or context.get_safety_compliance():
            chunks.append(self.create_technical_chunk(context))

        if context.get_cross_document_consistency():
            chunks.append(self.create_consistency_chunk(context))

        # Always create utility-specific chunk if we have any data
        if context.get_all_data():
            chunks.append(self.create_utility_specific_chunk(context))

        return chunks
    
    def create_tier_chunk(self, context) -> Dict:
        """Create tier classification and requirement chunk for solar permit validation"""
        return {
            'id': 'tier_validation',
            'type': 'TIER_CLASSIFICATION',
            'data': {
                'system_capacity_ac': context.get_system_capacity_ac(),
                'system_capacity_dc': context.get_system_capacity_dc(),
                'has_battery': context.has_battery(),
                'system_type': context.get_system_type(),
                'battery_specs': context.get_battery_specs(),
                'utility_provider': context.get_utility_provider(),
                'utility_requirements': context.get_utility_requirements()
            },
            'rules': self.utility.get_tier_rules(),
            'prompt_template': 'tier_classification_prompt',
            'priority': 'HIGH'
        }

    def create_document_chunk(self, context) -> Dict:
        """Create document field validation chunk for solar permit completeness"""
        return {
            'id': 'document_validation',
            'type': 'DOCUMENT_COMPLETENESS',
            'data': {
                'project_info': context.get_project_info(),
                'contractor_info': context.get_contractor_info(),
                'document_completeness': context.get_document_completeness(),
                'critical_gaps': context.get_critical_gaps(),
                'system_profile': {
                    'capacity_dc_kw': context.get_system_capacity_dc(),
                    'capacity_ac_kw': context.get_system_capacity_ac(),
                    'modules': context.get_module_specs(),
                    'battery': context.get_battery_specs()
                }
            },
            'rules': self.utility.get_document_field_requirements(),
            'prompt_template': 'document_completeness_prompt',
            'priority': 'HIGH'
        }

    def create_technical_chunk(self, context) -> Dict:
        """Create technical standards validation chunk for solar installation compliance"""
        return {
            'id': 'technical_validation',
            'type': 'TECHNICAL_COMPLIANCE',
            'data': {
                'electrical_validation': context.get_electrical_validation(),
                'safety_compliance': context.get_safety_compliance(),
                'structural_data': context.get_structural_data(),
                'module_specs': context.get_module_specs(),
                'main_panel_info': context.get_main_panel_info(),
                'conductor_specs': context.get_conductor_specs(),
                'grounding_info': context.get_grounding_info(),
                'rapid_shutdown': context.get_rapid_shutdown_info(),
                'fire_setbacks': context.get_fire_setbacks()
            },
            'rules': self.utility.get_technical_standards(),
            'prompt_template': 'technical_compliance_prompt',
            'priority': 'CRITICAL'
        }

    def create_consistency_chunk(self, context) -> Dict:
        """Create cross-document consistency validation chunk"""
        return {
            'id': 'consistency_validation',
            'type': 'CROSS_DOCUMENT_CONSISTENCY',
            'data': {
                'cross_document_consistency': context.get_cross_document_consistency(),
                'project_info': context.get_project_info(),
                'system_profile': {
                    'capacity_dc_kw': context.get_system_capacity_dc(),
                    'capacity_ac_kw': context.get_system_capacity_ac(),
                    'modules': context.get_module_specs(),
                    'type': context.get_system_type()
                },
                'calculated_capacity': context._calculate_module_capacity(),
                'utility_consistency': {
                    'project_utility': context.get_utility_provider(),
                    'requirements_utility': context.get_utility_requirements().get('provider', 'NOT_SPECIFIED')
                }
            },
            'rules': self.utility.get_consistency_rules(),
            'prompt_template': 'consistency_validation_prompt',
            'priority': 'MEDIUM'
        }

    def create_utility_specific_chunk(self, context) -> Dict:
        """Create utility-specific validation chunk for specialized requirements"""
        return {
            'id': 'utility_specific_validation',
            'type': 'UTILITY_SPECIFIC_REQUIREMENTS',
            'data': {
                'utility_provider': context.get_utility_provider(),
                'utility_requirements': context.get_utility_requirements(),
                'system_capacity_dc': context.get_system_capacity_dc(),
                'system_capacity_ac': context.get_system_capacity_ac(),
                'system_type': context.get_system_type(),
                'has_battery': context.has_battery(),
                'interconnection_data': context.get_interconnection_data(),
                'safety_compliance': context.get_safety_compliance(),
                'electrical_validation': context.get_electrical_validation(),
                'project_location': context.get_property_address(),
                'validation_summary': context.get_validation_summary()
            },
            'rules': self.utility.get_utility_specific_rules(),
            'prompt_template': 'utility_specific_requirements_prompt',
            'priority': 'HIGH'
        }

    def create_safety_focused_chunk(self, context) -> Dict:
        """Create safety-focused validation chunk for critical safety requirements"""
        return {
            'id': 'safety_validation',
            'type': 'SAFETY_CRITICAL',
            'data': {
                'safety_compliance': context.get_safety_compliance(),
                'electrical_validation': context.get_electrical_validation(),
                'rapid_shutdown': context.get_rapid_shutdown_info(),
                'disconnects': context.get_disconnect_info(),
                'grounding': context.get_grounding_info(),
                'fire_setbacks': context.get_fire_setbacks(),
                'conductor_specs': context.get_conductor_specs(),
                'critical_gaps': [gap for gap in context.get_critical_gaps()
                                if any(keyword in gap.lower() for keyword in ['safety', 'grounding', 'disconnect', 'egc', 'rapid'])]
            },
            'rules': self.utility.get_safety_rules(),
            'prompt_template': 'safety_critical_prompt',
            'priority': 'CRITICAL'
        }

    def get_chunk_priority_order(self, chunks: List[Dict]) -> List[Dict]:
        """Sort chunks by priority for processing order"""
        priority_order = {'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
        return sorted(chunks, key=lambda x: priority_order.get(x.get('priority', 'LOW'), 3))