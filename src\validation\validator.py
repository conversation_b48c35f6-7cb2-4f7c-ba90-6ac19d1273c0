# src/validation/validator.py
"""
Three-Layer Solar Planset Validation System
Layer 1: Deterministic Rule-Based Calculations
Layer 2: Intelligent Contextual Validation
Layer 3: Result Compilation & Status Decision
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from .context import ValidationContext
from .chunks import ValidationChunker
from .gemini_client import GeminiValidationClient
from .results import SolarPlansetValidator, ValidationResult as SolarValidationResult, ValidationIssue, IssueSeverity, SubmissionStatus
from .utilities.base_utility import BaseUtility, DefaultUtility

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValidationLayer(Enum):
    LAYER1_DETERMINISTIC = "layer1_deterministic"
    LAYER2_CONTEXTUAL = "layer2_contextual"
    LAYER3_COMPILATION = "layer3_compilation"

class ValidationMode(Enum):
    FULL_VALIDATION = "full"
    QUICK_CHECK = "quick"
    CRITICAL_ONLY = "critical"

@dataclass
class LayerResult:
    layer: ValidationLayer
    status: str
    issues: List[ValidationIssue]
    confidence: float
    processing_time: float
    metadata: Dict[str, Any]

class ThreeLayerValidator:
    """
    Main Three-Layer Solar Planset Validation System
    Combines deterministic rules, AI contextual analysis, and intelligent result compilation
    """

    def __init__(self, utility_config: Dict, validation_mode: ValidationMode = ValidationMode.FULL_VALIDATION):
        self.utility_config = utility_config
        self.validation_mode = validation_mode

        # Initialize utility-specific validator
        self.utility = self._load_utility_plugin(utility_config.get('utility_name', 'base'))

        # Initialize Layer 1: Deterministic validator (from results.py)
        self.layer1_validator = SolarPlansetValidator()

        # Initialize Layer 2: Contextual AI validator
        self.gemini_client = GeminiValidationClient()
        self.chunker = ValidationChunker(self.utility)

        # Validation history and caching
        self.validation_cache = {}
        self.validation_history = []

        logger.info(f"Three-Layer Validator initialized for {utility_config.get('utility_name', 'base')} utility")

    async def validate_planset(self, extracted_planset_data: Dict) -> Dict[str, Any]:
        """
        Main validation entry point - orchestrates all three layers
        """
        start_time = datetime.now()
        validation_id = f"val_{int(start_time.timestamp())}"

        logger.info(f"Starting three-layer validation {validation_id}")

        try:
            # Build validation context
            context = ValidationContext(extracted_planset_data, self.utility)

            # Layer 1: Deterministic Rule-Based Calculations
            layer1_result = await self._run_layer1_validation(extracted_planset_data, context)

            # Layer 2: Intelligent Contextual Validation (only if not critical failures in Layer 1)
            layer2_result = None
            if self.validation_mode != ValidationMode.CRITICAL_ONLY or not self._has_critical_issues(layer1_result):
                layer2_result = await self._run_layer2_validation(extracted_planset_data, context)

            # Layer 3: Result Compilation & Status Decision
            layer3_result = await self._run_layer3_compilation(layer1_result, layer2_result, context)

            # Compile final validation report
            final_report = self._compile_final_report(
                validation_id, layer1_result, layer2_result, layer3_result,
                start_time, extracted_planset_data
            )

            # Cache results for future reference
            self.validation_cache[validation_id] = final_report
            self.validation_history.append(validation_id)

            logger.info(f"Validation {validation_id} completed successfully")
            return final_report

        except Exception as e:
            logger.error(f"Validation {validation_id} failed: {str(e)}")
            return {
                "validation_id": validation_id,
                "status": "error",
                "error": str(e),
                "timestamp": start_time.isoformat()
            }

    async def _run_layer1_validation(self, data: Dict, context: ValidationContext) -> LayerResult:
        """
        Layer 1: Deterministic Rule-Based Calculations
        Fast, reliable validation of hard requirements (NEC codes, utility rules, safety)
        """
        start_time = datetime.now()
        logger.info("Running Layer 1: Deterministic Rule-Based Validation")

        try:
            # Use the existing SolarPlansetValidator for deterministic validation
            validation_result = self.layer1_validator.validate_planset(data)

            # Convert to LayerResult format
            layer1_result = LayerResult(
                layer=ValidationLayer.LAYER1_DETERMINISTIC,
                status=validation_result.status.value,
                issues=validation_result.issues,
                confidence=1.0,  # Deterministic rules have 100% confidence
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    "rules_checked": [
                        "busbar_calculation", "system_sizing", "electrical_requirements",
                        "safety_compliance", "document_completeness", "critical_gaps"
                    ],
                    "total_issues": len(validation_result.issues),
                    "critical_issues": len([i for i in validation_result.issues if i.severity == IssueSeverity.CRITICAL]),
                    "validation_summary": validation_result.summary
                }
            )

            logger.info(f"Layer 1 completed: {len(validation_result.issues)} issues found")
            return layer1_result

        except Exception as e:
            logger.error(f"Layer 1 validation failed: {str(e)}")
            return LayerResult(
                layer=ValidationLayer.LAYER1_DETERMINISTIC,
                status="error",
                issues=[],
                confidence=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={"error": str(e)}
            )

    async def _run_layer2_validation(self, data: Dict, context: ValidationContext) -> LayerResult:
        """
        Layer 2: Intelligent Contextual Validation
        AI-powered analysis for complex relationships, contextual understanding, and edge cases
        """
        start_time = datetime.now()
        logger.info("Running Layer 2: Intelligent Contextual Validation")

        try:
            # Create validation chunks for AI analysis
            chunks = self.chunker.create_chunks(context)

            # Validate chunks in parallel using Gemini
            chunk_results = await self._validate_chunks_parallel(chunks)

            # Process AI responses and convert to ValidationIssue format
            ai_issues = self._process_ai_chunk_results(chunk_results)

            # Calculate overall confidence based on AI responses
            confidence = self._calculate_ai_confidence(chunk_results)

            layer2_result = LayerResult(
                layer=ValidationLayer.LAYER2_CONTEXTUAL,
                status="completed",
                issues=ai_issues,
                confidence=confidence,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    "chunks_processed": len(chunks),
                    "ai_model": "gemini-2.0-flash-exp",
                    "chunk_types": [chunk.get('type') for chunk in chunks],
                    "total_ai_issues": len(ai_issues)
                }
            )

            logger.info(f"Layer 2 completed: {len(ai_issues)} contextual issues found")
            return layer2_result

        except Exception as e:
            logger.error(f"Layer 2 validation failed: {str(e)}")
            return LayerResult(
                layer=ValidationLayer.LAYER2_CONTEXTUAL,
                status="error",
                issues=[],
                confidence=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={"error": str(e)}
            )

    async def _run_layer3_compilation(self, layer1_result: LayerResult, layer2_result: Optional[LayerResult], context: ValidationContext) -> LayerResult:
        """
        Layer 3: Result Compilation & Status Decision
        Intelligent aggregation, deduplication, prioritization, and final status determination
        """
        start_time = datetime.now()
        logger.info("Running Layer 3: Result Compilation & Status Decision")

        try:
            # Combine issues from both layers
            all_issues = layer1_result.issues.copy()
            if layer2_result:
                all_issues.extend(layer2_result.issues)

            # Deduplicate similar issues
            deduplicated_issues = self._deduplicate_issues(all_issues)

            # Prioritize issues based on utility requirements and impact
            prioritized_issues = self._prioritize_issues(deduplicated_issues, context)

            # Determine final submission status
            final_status = self._determine_final_status(prioritized_issues)

            # Calculate overall confidence
            overall_confidence = self._calculate_overall_confidence(layer1_result, layer2_result)

            layer3_result = LayerResult(
                layer=ValidationLayer.LAYER3_COMPILATION,
                status=final_status,
                issues=prioritized_issues,
                confidence=overall_confidence,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    "original_issues_count": len(all_issues),
                    "deduplicated_count": len(deduplicated_issues),
                    "final_count": len(prioritized_issues),
                    "critical_issues": len([i for i in prioritized_issues if i.severity == IssueSeverity.CRITICAL]),
                    "major_issues": len([i for i in prioritized_issues if i.severity == IssueSeverity.MAJOR]),
                    "minor_issues": len([i for i in prioritized_issues if i.severity == IssueSeverity.MINOR]),
                    "final_status": final_status
                }
            )

            logger.info(f"Layer 3 completed: Final status = {final_status}")
            return layer3_result

        except Exception as e:
            logger.error(f"Layer 3 compilation failed: {str(e)}")
            return LayerResult(
                layer=ValidationLayer.LAYER3_COMPILATION,
                status="error",
                issues=[],
                confidence=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={"error": str(e)}
            )

    # Helper Methods for Layer Operations

    async def _validate_chunks_parallel(self, chunks: List[Dict]) -> List[Dict]:
        """Validate multiple chunks in parallel using Gemini"""
        tasks = []
        for chunk in chunks:
            task = self._validate_single_chunk(chunk)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if not isinstance(r, Exception)]

    async def _validate_single_chunk(self, chunk: Dict) -> Dict:
        """Validate a single chunk using Gemini"""
        try:
            validation_prompt = self._build_chunk_prompt(chunk)

            response = await self.gemini_client.validate_chunk(
                prompt=validation_prompt,
                chunk_data=chunk['data'],
                utility_rules=chunk['rules']
            )

            return {
                'chunk_type': chunk['type'],
                'chunk_id': chunk['id'],
                'validation_result': response,
                'success': True
            }
        except Exception as e:
            logger.error(f"Chunk validation failed for {chunk.get('id', 'unknown')}: {str(e)}")
            return {
                'chunk_type': chunk.get('type', 'unknown'),
                'chunk_id': chunk.get('id', 'unknown'),
                'validation_result': None,
                'success': False,
                'error': str(e)
            }

    def _build_chunk_prompt(self, chunk: Dict) -> str:
        """Build validation prompt for a specific chunk"""
        chunk_type = chunk.get('type', 'UNKNOWN')

        base_prompt = f"""
        You are an expert solar installation permit validator for {self.utility_config.get('utility_name', 'utility')} utility.

        VALIDATION TASK: {chunk_type}

        EXTRACTED DATA TO VALIDATE:
        {json.dumps(chunk.get('data', {}), indent=2)}

        UTILITY REQUIREMENTS TO CHECK:
        {json.dumps(chunk.get('rules', {}), indent=2)}

        VALIDATION INSTRUCTIONS:
        1. Check if the extracted data fulfills each specific utility requirement
        2. Look for actual compliance evidence, not just keywords
        3. Verify technical specifications match utility standards
        4. Identify any missing critical information
        5. Check for potential safety or code violations

        Provide validation results in this exact JSON format:
        {{
            "status": "pass" or "fail" or "warning",
            "confidence": 0.0-1.0,
            "validated_items": ["specific items that passed with evidence"],
            "failed_items": ["specific items that failed with detailed reasons"],
            "warnings": ["items that need attention but don't fail"],
            "missing_items": ["required items not found in the data"],
            "suggestions": ["actionable improvement suggestions"],
            "explanation": "detailed analysis of how content meets/fails requirements",
            "evidence_found": ["specific data that supports validation"],
            "critical_issues": ["issues that would cause immediate rejection"]
        }}

        Be thorough and specific in your analysis. Focus on utility compliance and safety requirements.
        """

        return base_prompt

    def _process_ai_chunk_results(self, chunk_results: List[Dict]) -> List[ValidationIssue]:
        """Process AI chunk results and convert to ValidationIssue objects"""
        issues = []

        for result in chunk_results:
            if not result.get('success', False):
                continue

            validation_result = result.get('validation_result', {})
            chunk_type = result.get('chunk_type', 'UNKNOWN')

            # Parse AI response
            if isinstance(validation_result, str):
                try:
                    validation_result = json.loads(validation_result)
                except json.JSONDecodeError:
                    continue

            # Convert failed items to ValidationIssue objects
            failed_items = validation_result.get('failed_items', [])
            for item in failed_items:
                issues.append(ValidationIssue(
                    severity=IssueSeverity.MAJOR,
                    category=f"AI_{chunk_type}",
                    description=item,
                    fix_recommendation=f"Address {chunk_type.lower()} validation failure",
                    rule_reference=f"AI_VALIDATION_{chunk_type}"
                ))

            # Convert critical issues
            critical_issues = validation_result.get('critical_issues', [])
            for item in critical_issues:
                issues.append(ValidationIssue(
                    severity=IssueSeverity.CRITICAL,
                    category=f"AI_{chunk_type}_CRITICAL",
                    description=item,
                    fix_recommendation=f"Critical: {item}",
                    rule_reference=f"AI_CRITICAL_{chunk_type}"
                ))

            # Convert warnings to minor issues
            warnings = validation_result.get('warnings', [])
            for item in warnings:
                issues.append(ValidationIssue(
                    severity=IssueSeverity.MINOR,
                    category=f"AI_{chunk_type}_WARNING",
                    description=item,
                    fix_recommendation=f"Review: {item}",
                    rule_reference=f"AI_WARNING_{chunk_type}"
                ))

        return issues

    def _deduplicate_issues(self, issues: List[ValidationIssue]) -> List[ValidationIssue]:
        """Remove duplicate or very similar issues"""
        if not issues:
            return []

        deduplicated = []
        seen_descriptions = set()

        for issue in issues:
            # Create a normalized description for comparison
            normalized_desc = issue.description.lower().strip()

            # Check for exact duplicates
            if normalized_desc in seen_descriptions:
                continue

            # Check for similar issues (simple similarity check)
            is_similar = False
            for existing_desc in seen_descriptions:
                if self._are_descriptions_similar(normalized_desc, existing_desc):
                    is_similar = True
                    break

            if not is_similar:
                deduplicated.append(issue)
                seen_descriptions.add(normalized_desc)

        logger.info(f"Deduplicated {len(issues)} issues to {len(deduplicated)}")
        return deduplicated

    def _are_descriptions_similar(self, desc1: str, desc2: str, threshold: float = 0.8) -> bool:
        """Check if two descriptions are similar using simple word overlap"""
        words1 = set(desc1.split())
        words2 = set(desc2.split())

        if not words1 or not words2:
            return False

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        similarity = len(intersection) / len(union) if union else 0
        return similarity >= threshold

    def _prioritize_issues(self, issues: List[ValidationIssue], context: ValidationContext) -> List[ValidationIssue]:
        """Prioritize issues based on utility requirements and impact"""
        if not issues:
            return []

        # Sort by severity first (Critical > Major > Minor)
        severity_order = {
            IssueSeverity.CRITICAL: 0,
            IssueSeverity.MAJOR: 1,
            IssueSeverity.MINOR: 2
        }

        # Add utility-specific priority scoring
        for issue in issues:
            issue.priority_score = self._calculate_issue_priority(issue, context)

        # Sort by severity first, then by priority score
        prioritized = sorted(issues, key=lambda x: (severity_order.get(x.severity, 3), -x.priority_score))

        return prioritized

    def _calculate_issue_priority(self, issue: ValidationIssue, context: ValidationContext) -> float:
        """Calculate priority score for an issue based on utility requirements"""
        score = 0.0

        # Base score by severity
        if issue.severity == IssueSeverity.CRITICAL:
            score += 100
        elif issue.severity == IssueSeverity.MAJOR:
            score += 50
        else:
            score += 10

        # Boost score for utility-specific critical areas
        critical_keywords = ['safety', 'grounding', 'disconnect', 'rapid shutdown', 'busbar', 'egc']
        for keyword in critical_keywords:
            if keyword.lower() in issue.description.lower():
                score += 20

        # Boost score for common rejection causes
        rejection_keywords = ['missing', 'not specified', 'inconsistent', 'mismatch', 'violation']
        for keyword in rejection_keywords:
            if keyword.lower() in issue.description.lower():
                score += 15

        return score

    def _determine_final_status(self, issues: List[ValidationIssue]) -> str:
        """Determine final submission status based on issues"""
        critical_count = len([i for i in issues if i.severity == IssueSeverity.CRITICAL])
        major_count = len([i for i in issues if i.severity == IssueSeverity.MAJOR])

        if critical_count > 0:
            return "FAIL"
        elif major_count > 3:
            return "NEEDS_MAJOR_REVISION"
        elif major_count > 0:
            return "NEEDS_REVIEW"
        else:
            return "PASS"

    def _calculate_ai_confidence(self, chunk_results: List[Dict]) -> float:
        """Calculate overall confidence from AI chunk results"""
        if not chunk_results:
            return 0.0

        confidences = []
        for result in chunk_results:
            if result.get('success', False):
                validation_result = result.get('validation_result', {})
                if isinstance(validation_result, dict):
                    confidence = validation_result.get('confidence', 0.5)
                    confidences.append(confidence)

        return sum(confidences) / len(confidences) if confidences else 0.5

    def _calculate_overall_confidence(self, layer1_result: LayerResult, layer2_result: Optional[LayerResult]) -> float:
        """Calculate overall validation confidence"""
        # Layer 1 has high confidence (deterministic rules)
        layer1_confidence = layer1_result.confidence

        # Layer 2 confidence from AI
        layer2_confidence = layer2_result.confidence if layer2_result else 0.8

        # Weighted average (Layer 1 more reliable)
        overall_confidence = (layer1_confidence * 0.7) + (layer2_confidence * 0.3)

        return min(overall_confidence, 1.0)

    def _has_critical_issues(self, layer_result: LayerResult) -> bool:
        """Check if layer result has critical issues"""
        return any(issue.severity == IssueSeverity.CRITICAL for issue in layer_result.issues)

    def _compile_final_report(self, validation_id: str, layer1_result: LayerResult,
                             layer2_result: Optional[LayerResult], layer3_result: LayerResult,
                             start_time: datetime, extracted_data: Dict) -> Dict[str, Any]:
        """Compile comprehensive final validation report"""

        total_time = (datetime.now() - start_time).total_seconds()

        # Extract key metrics
        total_issues = len(layer3_result.issues)
        critical_issues = len([i for i in layer3_result.issues if i.severity == IssueSeverity.CRITICAL])
        major_issues = len([i for i in layer3_result.issues if i.severity == IssueSeverity.MAJOR])
        minor_issues = len([i for i in layer3_result.issues if i.severity == IssueSeverity.MINOR])

        # Generate summary
        summary = self._generate_validation_summary(layer3_result.status, total_issues, critical_issues, major_issues, minor_issues)

        # Create detailed report
        final_report = {
            "validation_id": validation_id,
            "timestamp": start_time.isoformat(),
            "utility": self.utility_config.get('utility_name', 'base'),
            "validation_mode": self.validation_mode.value,
            "total_processing_time": total_time,

            # Overall Results
            "final_status": layer3_result.status,
            "overall_confidence": layer3_result.confidence,
            "summary": summary,

            # Issue Breakdown
            "issues_summary": {
                "total_issues": total_issues,
                "critical_issues": critical_issues,
                "major_issues": major_issues,
                "minor_issues": minor_issues
            },

            # Detailed Issues
            "issues": [self._issue_to_dict(issue) for issue in layer3_result.issues],

            # Layer Results
            "layer_results": {
                "layer1_deterministic": {
                    "status": layer1_result.status,
                    "issues_found": len(layer1_result.issues),
                    "processing_time": layer1_result.processing_time,
                    "confidence": layer1_result.confidence,
                    "metadata": layer1_result.metadata
                },
                "layer2_contextual": {
                    "status": layer2_result.status if layer2_result else "skipped",
                    "issues_found": len(layer2_result.issues) if layer2_result else 0,
                    "processing_time": layer2_result.processing_time if layer2_result else 0,
                    "confidence": layer2_result.confidence if layer2_result else 0,
                    "metadata": layer2_result.metadata if layer2_result else {}
                },
                "layer3_compilation": {
                    "status": layer3_result.status,
                    "final_issues": len(layer3_result.issues),
                    "processing_time": layer3_result.processing_time,
                    "confidence": layer3_result.confidence,
                    "metadata": layer3_result.metadata
                }
            },

            # System Information
            "system_info": {
                "project_customer": extracted_data.get('project_info', {}).get('customer', 'Unknown'),
                "system_capacity_dc": extracted_data.get('system_profile', {}).get('capacity_dc_kw', 0),
                "system_capacity_ac": extracted_data.get('system_profile', {}).get('capacity_ac_kw', 0),
                "system_type": extracted_data.get('system_profile', {}).get('type', 'Unknown'),
                "utility_provider": extracted_data.get('project_info', {}).get('utility', 'Unknown')
            },

            # Recommendations
            "recommendations": self._generate_recommendations(layer3_result.issues),

            # Next Steps
            "next_steps": self._generate_next_steps(layer3_result.status, layer3_result.issues)
        }

        return final_report

    def _generate_validation_summary(self, status: str, total: int, critical: int, major: int, minor: int) -> str:
        """Generate human-readable validation summary"""
        if status == "FAIL":
            return f"VALIDATION FAILED: {critical} critical issue(s) must be resolved before submission. Total issues: {total}"
        elif status == "NEEDS_MAJOR_REVISION":
            return f"MAJOR REVISIONS NEEDED: {major} major issue(s) require significant changes. Total issues: {total}"
        elif status == "NEEDS_REVIEW":
            return f"REVIEW REQUIRED: {major} major issue(s) should be addressed. Total issues: {total}"
        elif status == "PASS":
            return f"VALIDATION PASSED: Planset meets requirements for submission. {minor} minor issue(s) noted."
        else:
            return f"VALIDATION STATUS: {status}. Total issues: {total}"

    def _issue_to_dict(self, issue: ValidationIssue) -> Dict[str, Any]:
        """Convert ValidationIssue to dictionary format"""
        return {
            "severity": issue.severity.value,
            "category": issue.category,
            "description": issue.description,
            "fix_recommendation": issue.fix_recommendation,
            "rule_reference": issue.rule_reference,
            "priority_score": getattr(issue, 'priority_score', 0)
        }

    def _generate_recommendations(self, issues: List[ValidationIssue]) -> List[str]:
        """Generate actionable recommendations based on issues"""
        recommendations = []

        # Critical issue recommendations
        critical_issues = [i for i in issues if i.severity == IssueSeverity.CRITICAL]
        if critical_issues:
            recommendations.append("CRITICAL: Address all critical issues before resubmission")
            for issue in critical_issues[:3]:  # Top 3 critical issues
                recommendations.append(f"• {issue.fix_recommendation}")

        # Major issue recommendations
        major_issues = [i for i in issues if i.severity == IssueSeverity.MAJOR]
        if major_issues:
            recommendations.append("MAJOR: Review and address major issues to improve approval chances")
            for issue in major_issues[:2]:  # Top 2 major issues
                recommendations.append(f"• {issue.fix_recommendation}")

        # General recommendations
        if len(issues) > 5:
            recommendations.append("Consider comprehensive document review to address multiple issues")

        return recommendations

    def _generate_next_steps(self, status: str, issues: List[ValidationIssue]) -> List[str]:
        """Generate next steps based on validation status"""
        next_steps = []

        if status == "FAIL":
            next_steps.extend([
                "1. Address all critical issues identified in the validation",
                "2. Review and update affected documents",
                "3. Re-run validation to verify fixes",
                "4. Do not submit until all critical issues are resolved"
            ])
        elif status == "NEEDS_MAJOR_REVISION":
            next_steps.extend([
                "1. Review major issues and plan document revisions",
                "2. Update technical specifications and calculations",
                "3. Ensure cross-document consistency",
                "4. Re-validate before submission"
            ])
        elif status == "NEEDS_REVIEW":
            next_steps.extend([
                "1. Review major issues and consider addressing them",
                "2. Verify all required information is complete",
                "3. Consider re-validation after changes",
                "4. Submission may proceed with caution"
            ])
        elif status == "PASS":
            next_steps.extend([
                "1. Review minor issues for potential improvements",
                "2. Planset is ready for submission",
                "3. Keep validation report for records",
                "4. Monitor submission status with utility"
            ])

        return next_steps

    def _load_utility_plugin(self, utility_name: str) -> BaseUtility:
        """Load utility-specific validation plugin"""
        try:
            if utility_name.lower() == 'fpl':
                from .utilities.fpl import FPLUtility
                return FPLUtility()
            elif utility_name.lower() == 'srp':
                from .utilities.srp import SRPUtility
                return SRPUtility()
            else:
                from .utilities.base_utility import DefaultUtility
                return DefaultUtility()
        except ImportError as e:
            logger.warning(f"Could not load utility plugin for {utility_name}: {e}")
            from .utilities.base_utility import DefaultUtility
            return DefaultUtility()

# Convenience function for external use
async def validate_solar_planset(planset_data: Dict, utility_config: Dict,
                                validation_mode: ValidationMode = ValidationMode.FULL_VALIDATION) -> Dict[str, Any]:
    """
    Convenience function to validate a solar planset

    Args:
        planset_data: Extracted planset data in the new solar permit JSON format
        utility_config: Utility configuration including utility_name
        validation_mode: Validation mode (FULL_VALIDATION, QUICK_CHECK, CRITICAL_ONLY)

    Returns:
        Comprehensive validation report
    """
    validator = ThreeLayerValidator(utility_config, validation_mode)
    return await validator.validate_planset(planset_data)