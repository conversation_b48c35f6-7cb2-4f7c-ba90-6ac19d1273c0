#!/usr/bin/env python3
"""
Test script to verify the validator server can start and respond
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

async def test_validation_directly():
    """Test the validation function directly"""
    print("🧪 Testing validation function directly...")
    
    try:
        from validation.validator import validate_solar_planset, ValidationMode
        
        # Sample test data
        test_data = {
            "project_info": {
                "customer": "Test Customer",
                "address": "123 Test St",
                "utility": "APS",
                "contractor": "Test Solar Co"
            },
            "system_profile": {
                "capacity_dc_kw": 10.5,
                "module_count": 30,
                "inverter_count": 1
            }
        }
        
        utility_config = {"utility_name": "aps"}
        
        print("📋 Running quick validation...")
        result = await validate_solar_planset(
            test_data,
            utility_config,
            ValidationMode.QUICK_CHECK
        )
        
        print(f"✅ Validation completed!")
        print(f"📊 Status: {result.get('final_status', 'Unknown')}")
        print(f"🔍 Issues found: {len(result.get('all_issues', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_import():
    """Test if the API can be imported"""
    print("🔌 Testing API import...")
    
    try:
        from api.app import app
        print("✅ API import successful")
        return True
    except Exception as e:
        print(f"❌ API import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🌞 Solar Validator Server Test Suite")
    print("=" * 50)
    
    # Test 1: API Import
    api_ok = test_api_import()
    
    # Test 2: Direct validation
    validation_ok = await test_validation_directly()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  API Import: {'✅ PASS' if api_ok else '❌ FAIL'}")
    print(f"  Validation: {'✅ PASS' if validation_ok else '❌ FAIL'}")
    
    if api_ok and validation_ok:
        print("\n🎉 All tests passed! The validator is ready to use.")
        print("\n💡 To start the server manually:")
        print("   cd validatorFinal/src")
        print("   uvicorn api.app:app --host 0.0.0.0 --port 8001")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
