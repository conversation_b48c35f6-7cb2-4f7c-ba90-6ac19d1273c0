"""
Three-Layer Solar Planset Validation API
FastAPI application for validating solar permit plansets
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import our validation system
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from validation.validator import ThreeLayerValidator, ValidationMode, validate_solar_planset
from validation.results import validate_planset_from_json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Solar Planset Validation API",
    description="Three-Layer validation system for solar permit plansets",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class ValidationRequest(BaseModel):
    planset_data: Dict[str, Any] = Field(..., description="Extracted solar planset data in JSON format")
    utility_name: str = Field(default="base", description="Utility provider name (e.g., 'fpl', 'srp', 'base')")
    validation_mode: str = Field(default="full", description="Validation mode: 'full', 'quick', or 'critical'")
    include_ai_analysis: bool = Field(default=True, description="Include AI-powered contextual analysis")

class ValidationResponse(BaseModel):
    validation_id: str
    status: str
    summary: str
    total_issues: int
    critical_issues: int
    major_issues: int
    minor_issues: int
    overall_confidence: float
    processing_time: float
    recommendations: list
    next_steps: list

class QuickValidationRequest(BaseModel):
    planset_data: Dict[str, Any] = Field(..., description="Extracted solar planset data")

# Global validation cache
validation_cache = {}

@app.get("/")
async def root():
    """API health check and information"""
    return {
        "message": "Solar Planset Validation API",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "validate": "/validate - Full three-layer validation",
            "validate/quick": "/validate/quick - Quick deterministic validation only",
            "validate/status": "/validate/status/{validation_id} - Get validation status",
            "health": "/health - Health check"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "validation_cache_size": len(validation_cache)
    }

@app.post("/validate", response_model=ValidationResponse)
async def validate_planset(request: ValidationRequest, background_tasks: BackgroundTasks):
    """
    Full three-layer validation of solar planset

    Performs:
    - Layer 1: Deterministic rule-based validation
    - Layer 2: AI-powered contextual analysis (if enabled)
    - Layer 3: Result compilation and status decision
    """
    try:
        logger.info(f"Starting validation for utility: {request.utility_name}")

        # Validate input data
        if not request.planset_data:
            raise HTTPException(status_code=400, detail="Planset data is required")

        # Configure validation mode
        mode_mapping = {
            "full": ValidationMode.FULL_VALIDATION,
            "quick": ValidationMode.QUICK_CHECK,
            "critical": ValidationMode.CRITICAL_ONLY
        }
        validation_mode = mode_mapping.get(request.validation_mode.lower(), ValidationMode.FULL_VALIDATION)

        # Configure utility
        utility_config = {
            "utility_name": request.utility_name,
            "include_ai_analysis": request.include_ai_analysis
        }

        # Run validation
        start_time = datetime.now()
        validation_result = await validate_solar_planset(
            request.planset_data,
            utility_config,
            validation_mode
        )

        # Store in cache
        validation_id = validation_result.get("validation_id")
        validation_cache[validation_id] = validation_result

        # Extract response data
        issues_summary = validation_result.get("issues_summary", {})

        response = ValidationResponse(
            validation_id=validation_id,
            status=validation_result.get("final_status", "unknown"),
            summary=validation_result.get("summary", ""),
            total_issues=issues_summary.get("total_issues", 0),
            critical_issues=issues_summary.get("critical_issues", 0),
            major_issues=issues_summary.get("major_issues", 0),
            minor_issues=issues_summary.get("minor_issues", 0),
            overall_confidence=validation_result.get("overall_confidence", 0.0),
            processing_time=validation_result.get("total_processing_time", 0.0),
            recommendations=validation_result.get("recommendations", []),
            next_steps=validation_result.get("next_steps", [])
        )

        logger.info(f"Validation completed: {validation_id} - Status: {response.status}")
        return response

    except Exception as e:
        logger.error(f"Validation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")

@app.post("/validate/quick")
async def quick_validate_planset(request: QuickValidationRequest):
    """
    Quick deterministic validation only (Layer 1)
    Fast validation using only deterministic rules
    """
    try:
        logger.info("Starting quick validation")

        # Use the existing deterministic validator
        validation_report = validate_planset_from_json(request.planset_data)

        return {
            "validation_type": "quick_deterministic",
            "timestamp": datetime.now().isoformat(),
            "report": validation_report,
            "status": "completed"
        }

    except Exception as e:
        logger.error(f"Quick validation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Quick validation failed: {str(e)}")

@app.get("/validate/status/{validation_id}")
async def get_validation_status(validation_id: str):
    """Get detailed validation results by ID"""
    if validation_id not in validation_cache:
        raise HTTPException(status_code=404, detail="Validation ID not found")

    return validation_cache[validation_id]

@app.get("/validate/history")
async def get_validation_history():
    """Get validation history summary"""
    history = []
    for validation_id, result in validation_cache.items():
        history.append({
            "validation_id": validation_id,
            "timestamp": result.get("timestamp"),
            "utility": result.get("utility"),
            "status": result.get("final_status"),
            "total_issues": result.get("issues_summary", {}).get("total_issues", 0)
        })

    return {
        "total_validations": len(history),
        "history": sorted(history, key=lambda x: x["timestamp"], reverse=True)
    }

@app.delete("/validate/cache")
async def clear_validation_cache():
    """Clear validation cache"""
    global validation_cache
    cache_size = len(validation_cache)
    validation_cache.clear()

    return {
        "message": f"Cleared {cache_size} cached validations",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)