#!/usr/bin/env python3
"""
Test script for the Three-Layer Solar Planset Validation System
Tests the integration between extractor output and validator input
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from validation.validator import validate_solar_planset, ValidationMode
from validation.results import validate_planset_from_json

# Sample solar permit data in the new format (from extractor)
SAMPLE_SOLAR_PERMIT_DATA = {
    "project_info": {
        "customer": "<PERSON>",
        "address": "123 Solar Street, Phoenix, AZ 85001",
        "utility": "APS",
        "contractor": {
            "name": "Solar Pro Installers",
            "license": "ROC123456",
            "contact": "<EMAIL>"
        }
    },
    "system_profile": {
        "type": "SOLAR_PLUS_STORAGE",
        "capacity_dc_kw": 12.8,
        "capacity_ac_kw": 10.0,
        "modules": {
            "manufacturer": "Canadian Solar",
            "model": "CS3W-400P",
            "quantity": 32,
            "power_w": 400,
            "efficiency": 20.5
        },
        "battery": {
            "manufacturer": "Tesla",
            "model": "Powerwall 2",
            "capacity_kwh": 13.5,
            "quantity": 1
        }
    },
    "electrical_validation": {
        "main_panel": {
            "manufacturer": "Square D",
            "model": "QO",
            "bus_rating": 200,
            "main_breaker_derated": 175,
            "solar_breaker": 60,
            "bus_calculation": {
                "total_load": 235,
                "max_allowed": 240,
                "pass": True
            }
        },
        "conductors": {
            "dc_positive": "12 AWG THWN-2",
            "dc_negative": "12 AWG THWN-2",
            "egc_size": "12 AWG",
            "ac_conductors": "10 AWG THWN-2"
        },
        "grounding": {
            "method": "exothermic welding",
            "electrode": "ground rod",
            "conductor_size": "6 AWG"
        }
    },
    "safety_compliance": {
        "rapid_shutdown": {
            "compliance": True,
            "method": "module-level power electronics",
            "equipment": "SolarEdge optimizers"
        },
        "fire_setbacks": {
            "edges_in": 36,
            "ridge_in": 36,
            "compliant": True
        },
        "disconnects": {
            "ac_disconnect": "Square D 60A",
            "dc_disconnect": "Integrated in inverter",
            "location": "Adjacent to main panel"
        }
    },
    "structural_data": {
        "roof_type": "Composition shingle",
        "mounting_method": "Penetrating rail system",
        "wind_load_mph": 120,
        "snow_load_psf": 25,
        "engineering_stamp": True
    },
    "utility_requirements": {
        "provider": "APS",
        "interconnection_type": "Net metering",
        "requirements": {
            "external_disconnect": False,
            "production_meter": True,
            "study_required": False
        }
    },
    "field_level_document_completeness": {
        "application": {
            "customer_signature": True,
            "contractor_license": True,
            "system_specifications": True
        },
        "single_line_diagram": {
            "equipment_labels": True,
            "wire_specifications": True,
            "grounding_shown": True
        },
        "site_plan": {
            "setback_dimensions": True,
            "equipment_locations": True,
            "utility_connections": True
        }
    },
    "cross_document_consistency": {
        "system_capacity": {
            "consistent": True,
            "values": ["12.8 kW", "12.8 kW", "12.8 kW"]
        },
        "equipment_models": {
            "consistent": True,
            "inverter": ["SolarEdge SE10000H", "SolarEdge SE10000H"],
            "modules": ["CS3W-400P", "CS3W-400P"]
        }
    },
    "critical_gaps": [
        "Structural engineering stamp verification needed",
        "Battery interconnection details require clarification"
    ]
}

async def test_three_layer_validation():
    """Test the complete three-layer validation system"""
    print("🔍 Testing Three-Layer Solar Planset Validation System")
    print("=" * 60)
    
    # Test 1: Quick deterministic validation (Layer 1 only)
    print("\n📋 Test 1: Quick Deterministic Validation (Layer 1)")
    print("-" * 50)
    
    try:
        quick_result = validate_planset_from_json(SAMPLE_SOLAR_PERMIT_DATA)
        print("✅ Quick validation completed")
        print(f"📄 Report preview: {quick_result[:200]}...")
    except Exception as e:
        print(f"❌ Quick validation failed: {e}")
    
    # Test 2: Full three-layer validation
    print("\n🧠 Test 2: Full Three-Layer Validation")
    print("-" * 50)
    
    utility_config = {
        "utility_name": "aps",
        "include_ai_analysis": True
    }
    
    try:
        full_result = await validate_solar_planset(
            SAMPLE_SOLAR_PERMIT_DATA,
            utility_config,
            ValidationMode.FULL_VALIDATION
        )
        
        print("✅ Full validation completed")
        print(f"🆔 Validation ID: {full_result.get('validation_id')}")
        print(f"📊 Final Status: {full_result.get('final_status')}")
        print(f"⏱️  Processing Time: {full_result.get('total_processing_time', 0):.2f}s")
        print(f"🎯 Confidence: {full_result.get('overall_confidence', 0):.2f}")
        
        # Issues summary
        issues = full_result.get('issues_summary', {})
        print(f"🔴 Critical Issues: {issues.get('critical_issues', 0)}")
        print(f"🟡 Major Issues: {issues.get('major_issues', 0)}")
        print(f"🟢 Minor Issues: {issues.get('minor_issues', 0)}")
        
        # Layer breakdown
        layers = full_result.get('layer_results', {})
        print(f"\n📊 Layer Results:")
        for layer_name, layer_data in layers.items():
            print(f"  {layer_name}: {layer_data.get('status')} ({layer_data.get('issues_found', 0)} issues)")
        
        # Recommendations
        recommendations = full_result.get('recommendations', [])
        if recommendations:
            print(f"\n💡 Top Recommendations:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"  {i}. {rec}")
        
    except Exception as e:
        print(f"❌ Full validation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Critical-only validation
    print("\n⚠️  Test 3: Critical Issues Only Validation")
    print("-" * 50)
    
    try:
        critical_result = await validate_solar_planset(
            SAMPLE_SOLAR_PERMIT_DATA,
            utility_config,
            ValidationMode.CRITICAL_ONLY
        )
        
        print("✅ Critical validation completed")
        print(f"📊 Status: {critical_result.get('final_status')}")
        
        critical_issues = critical_result.get('issues_summary', {}).get('critical_issues', 0)
        print(f"🔴 Critical Issues Found: {critical_issues}")
        
    except Exception as e:
        print(f"❌ Critical validation failed: {e}")

def test_data_with_issues():
    """Create test data with known issues for validation testing"""
    problematic_data = SAMPLE_SOLAR_PERMIT_DATA.copy()
    
    # Introduce some issues
    problematic_data["electrical_validation"]["conductors"]["egc_size"] = "NOT SPECIFIED"
    problematic_data["electrical_validation"]["main_panel"]["bus_calculation"]["pass"] = False
    problematic_data["safety_compliance"]["rapid_shutdown"]["compliance"] = False
    problematic_data["critical_gaps"].extend([
        "EGC conductor size not specified anywhere",
        "Busbar calculation fails NEC 705.12 requirements",
        "Rapid shutdown compliance not verified"
    ])
    
    return problematic_data

async def test_problematic_planset():
    """Test validation with a planset that has multiple issues"""
    print("\n🚨 Test 4: Problematic Planset Validation")
    print("-" * 50)
    
    problematic_data = test_data_with_issues()
    
    utility_config = {"utility_name": "base"}
    
    try:
        result = await validate_solar_planset(
            problematic_data,
            utility_config,
            ValidationMode.FULL_VALIDATION
        )
        
        print("✅ Problematic planset validation completed")
        print(f"📊 Final Status: {result.get('final_status')}")
        
        issues = result.get('issues_summary', {})
        print(f"🔴 Critical Issues: {issues.get('critical_issues', 0)}")
        print(f"🟡 Major Issues: {issues.get('major_issues', 0)}")
        print(f"🟢 Minor Issues: {issues.get('minor_issues', 0)}")
        
        # Show some specific issues
        detailed_issues = result.get('issues', [])
        if detailed_issues:
            print(f"\n🔍 Sample Issues Found:")
            for i, issue in enumerate(detailed_issues[:3], 1):
                print(f"  {i}. [{issue.get('severity')}] {issue.get('description')}")
        
    except Exception as e:
        print(f"❌ Problematic validation failed: {e}")

async def main():
    """Run all validation tests"""
    print("🌞 Solar Planset Three-Layer Validation Test Suite")
    print("=" * 60)
    
    await test_three_layer_validation()
    await test_problematic_planset()
    
    print("\n✅ All tests completed!")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
